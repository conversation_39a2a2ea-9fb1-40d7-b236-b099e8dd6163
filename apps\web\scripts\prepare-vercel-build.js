#!/usr/bin/env node

/**
 * Vercel Build Preparation Script
 * This script prepares the web app for Vercel deployment by:
 * 1. Copying workspace packages into the web app
 * 2. Replacing the package.json with Vercel-compatible version
 * 3. Installing dependencies
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 Preparing Vercel build...');

// Paths
const webDir = process.cwd();
const rootDir = path.resolve(webDir, '../..');
const packagesDir = path.resolve(rootDir, 'packages');
const nodeModulesDir = path.resolve(webDir, 'node_modules');

// Ensure node_modules exists
if (!fs.existsSync(nodeModulesDir)) {
  fs.mkdirSync(nodeModulesDir, { recursive: true });
}

// List of packages to copy
const packages = [
  'api-client',
  'business-logic', 
  'config',
  'database',
  'firebase-config',
  'shared-types',
  'shared-ui',
  'shared-utils'
];

console.log('📦 Copying workspace packages...');

packages.forEach(packageName => {
  const srcPath = path.resolve(packagesDir, packageName);
  const destPath = path.resolve(nodeModulesDir, packageName);
  
  if (fs.existsSync(srcPath)) {
    console.log(`  Copying ${packageName}...`);
    
    // Remove existing if it exists
    if (fs.existsSync(destPath)) {
      fs.rmSync(destPath, { recursive: true, force: true });
    }
    
    // Copy the package
    copyRecursive(srcPath, destPath);

    // Fix workspace references in the copied package.json
    try {
      fixWorkspaceReferences(destPath);
    } catch (error) {
      console.warn(`    Warning: Could not fix workspace references for ${packageName}:`, error.message);
    }

    console.log(`  ✅ ${packageName} copied and fixed`);
  } else {
    console.warn(`  ⚠️  Package ${packageName} not found at ${srcPath}`);
  }
});

console.log('📄 Replacing package.json...');

// Backup original package.json
const originalPackageJson = path.resolve(webDir, 'package.json');
const backupPackageJson = path.resolve(webDir, 'package.json.backup');
const vercelPackageJson = path.resolve(webDir, 'package.vercel.json');

if (fs.existsSync(originalPackageJson)) {
  fs.copyFileSync(originalPackageJson, backupPackageJson);
}

if (fs.existsSync(vercelPackageJson)) {
  fs.copyFileSync(vercelPackageJson, originalPackageJson);
  console.log('  ✅ package.json replaced with Vercel-compatible version');
} else {
  console.error('  ❌ package.vercel.json not found');
  process.exit(1);
}

console.log('🗑️  Removing .npmrc for Vercel compatibility...');

// Remove .npmrc to prevent pnpm conflicts
const npmrcPath = path.resolve(webDir, '.npmrc');
const npmrcBackupPath = path.resolve(webDir, '.npmrc.backup');

if (fs.existsSync(npmrcPath)) {
  fs.copyFileSync(npmrcPath, npmrcBackupPath);
  fs.unlinkSync(npmrcPath);
  console.log('  ✅ .npmrc removed and backed up');
}

console.log('🧹 Cleaning npm cache and lock files...');

// Remove any existing lock files
const lockFiles = ['package-lock.json', 'pnpm-lock.yaml', 'yarn.lock'];
lockFiles.forEach(lockFile => {
  const lockPath = path.resolve(webDir, lockFile);
  if (fs.existsSync(lockPath)) {
    fs.unlinkSync(lockPath);
    console.log(`  ✅ ${lockFile} removed`);
  }
});

console.log('✅ Vercel build preparation complete!');

/**
 * Fix workspace references in package.json
 */
function fixWorkspaceReferences(packagePath) {
  const packageJsonPath = path.resolve(packagePath, 'package.json');

  if (fs.existsSync(packageJsonPath)) {
    console.log(`    Fixing workspace references in ${packageJsonPath}`);
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    let modified = false;

    // Fix dependencies
    if (packageJson.dependencies) {
      for (const [dep, version] of Object.entries(packageJson.dependencies)) {
        if (typeof version === 'string' && version.startsWith('workspace:')) {
          packageJson.dependencies[dep] = '*';
          modified = true;
        }
      }
    }

    // Fix devDependencies
    if (packageJson.devDependencies) {
      for (const [dep, version] of Object.entries(packageJson.devDependencies)) {
        if (typeof version === 'string' && version.startsWith('workspace:')) {
          packageJson.devDependencies[dep] = '*';
          modified = true;
        }
      }
    }

    if (modified) {
      fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
      console.log(`    ✅ Fixed workspace references in ${path.basename(packagePath)}`);
    } else {
      console.log(`    ℹ️  No workspace references found in ${path.basename(packagePath)}`);
    }
  }
}

/**
 * Recursively copy directory
 */
function copyRecursive(src, dest) {
  const stat = fs.statSync(src);
  
  if (stat.isDirectory()) {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }
    
    const files = fs.readdirSync(src);
    files.forEach(file => {
      // Skip node_modules and dist directories
      if (file === 'node_modules' || file === '.git') {
        return;
      }
      
      const srcFile = path.resolve(src, file);
      const destFile = path.resolve(dest, file);
      copyRecursive(srcFile, destFile);
    });
  } else {
    fs.copyFileSync(src, dest);
  }
}
