{"version": 2, "framework": "nextjs", "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm install", "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "env": {"NODE_VERSION": "20", "NEXT_TELEMETRY_DISABLED": "1"}, "build": {"env": {"NEXT_TELEMETRY_DISABLED": "1", "NODE_ENV": "production"}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "redirects": [{"source": "/", "destination": "/home", "permanent": false}], "rewrites": [{"source": "/firebase-messaging-sw.js", "destination": "/firebase-messaging-sw.js"}]}